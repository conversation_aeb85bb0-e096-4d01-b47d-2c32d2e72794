# Batch Processing Setup Guide

## Overview

This guide explains how to set up and use the daily batch processing system for your insurance policy management application. The system runs automatically at 1 AM every day to process premium dues, applications, and policy status updates.

## Architecture

The batch processing system consists of:

1. **BatchProcessor** (`batch_processor.py`) - Core processing logic for all 5 tasks
2. **BatchScheduler** (`scheduler.py`) - APScheduler integration for daily scheduling
3. **FastAPI Integration** (`main.py`) - API endpoints and application lifecycle management

## Installation

### 1. Install Dependencies

```bash
# Activate your virtual environment
source env/Scripts/activate  # Windows
# or
source env/bin/activate      # Linux/Mac

# Install required packages
pip install -r requirements.txt
```

### 2. Database Setup

The batch processing system uses your existing PostgreSQL database. It will create a job store table for APScheduler to persist scheduled jobs.

### 3. Configuration

The system uses your existing database configuration from `db.py`. No additional configuration is required.

## Features

### Daily Batch Processing Tasks

The system performs 5 main tasks every day at 1 AM:

#### Task 1: Premium Due Activity Generation
- Checks all policies for premium due dates based on frequency (monthly, quarterly, annually, weekly)
- Creates "Premium Due" activities in the `policyactivities` table
- Uses the policy's `firstdeductiondate` and `frequency` to calculate due dates

#### Task 2: Premium Applied Activity Creation
- Checks for premium due activities in the current month
- Compares with "Suspense Amount" activities
- Creates "Premium Applied" activities when sufficient funds are available
- Updates suspense amounts after premium application

#### Task 3: Policy Summary Updates
- Updates or creates records in the `policysummary` table
- Calculates:
  - `premiumsdue`: Count of "Premium Due" activities
  - `premiumspaid`: Count of "Premium Applied" activities
  - `totalpremiumspaid`: Sum of "Money In" activities
  - `outstanding`: Difference between due and paid premiums

#### Task 4: Policy Issued Activity Creation
- Creates "Policy Issued" activities for policies with premium applications
- Uses the effective date of the first "Premium Applied" activity

#### Task 5: Policy Status Determination
- Updates policy status based on outstanding premiums:
  - Outstanding = 0: "Active"
  - Outstanding = 1: "Grace"
  - Outstanding 2-4: "Reinstatement"
  - Outstanding ≥ 5: "Lapse"

## API Endpoints

### Manual Batch Processing
```http
POST /batch/run-now/
```
Manually trigger batch processing for testing purposes.

### View Scheduled Jobs
```http
GET /batch/jobs/
```
Get information about scheduled batch jobs.

### Test Individual Tasks
```http
POST /batch/test-individual-tasks/
```
Test each batch processing task individually for development and debugging.

## Usage

### Starting the Application

```bash
# Start the FastAPI application
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

The scheduler will automatically start when the application starts and will:
- Schedule the daily batch job for 1 AM
- Persist the job in the database
- Handle application restarts gracefully

### Manual Testing

You can test the batch processing system using the API endpoints:

```bash
# Test individual tasks
curl -X POST "http://localhost:8000/batch/test-individual-tasks/"

# Trigger full batch processing
curl -X POST "http://localhost:8000/batch/run-now/"

# Check scheduled jobs
curl -X GET "http://localhost:8000/batch/jobs/"
```

### Monitoring

The system provides comprehensive logging:

- **Console Output**: Real-time logging during development
- **Log File**: `batch_processing.log` for production monitoring
- **Database Persistence**: Job execution history in APScheduler tables

## Logging

Logs are written to:
1. Console (stdout)
2. `batch_processing.log` file
3. APScheduler job execution events

Log levels:
- **INFO**: Normal operation, task completion
- **WARNING**: Non-critical issues (e.g., missing data)
- **ERROR**: Critical errors that require attention

## Error Handling

The system includes robust error handling:

- **Database Rollback**: Automatic rollback on errors
- **Individual Task Isolation**: Errors in one task don't affect others
- **Retry Logic**: APScheduler handles job retries
- **Grace Period**: 5-minute grace period for missed executions

## Timezone Configuration

The scheduler is configured for `Africa/Blantyre` timezone. To change:

1. Edit `scheduler.py`
2. Update the `timezone` parameter in `setup_scheduler()`
3. Restart the application

## Production Considerations

### 1. Database Performance
- Consider adding indexes on frequently queried columns
- Monitor query performance during batch processing
- Schedule maintenance windows if needed

### 2. Monitoring
- Set up log monitoring and alerting
- Monitor batch execution times
- Track job success/failure rates

### 3. Backup Strategy
- Ensure database backups before batch processing
- Consider point-in-time recovery capabilities

### 4. Scaling
- The current implementation handles single-instance deployment
- For multiple instances, consider using Redis as APScheduler job store

## Troubleshooting

### Common Issues

1. **Scheduler Not Starting**
   - Check database connectivity
   - Verify APScheduler dependencies
   - Review application logs

2. **Jobs Not Executing**
   - Check timezone configuration
   - Verify job persistence in database
   - Review APScheduler logs

3. **Database Errors**
   - Check connection string
   - Verify table permissions
   - Monitor connection pool

### Debug Mode

Enable debug logging by modifying `batch_processor.py`:

```python
logging.basicConfig(level=logging.DEBUG)
```

## Support

For issues or questions:
1. Check the log files for error details
2. Use the test endpoints to isolate problems
3. Review the database for data consistency
4. Monitor APScheduler job store tables

## Next Steps

After setup:
1. Test the system with sample data
2. Monitor the first few batch runs
3. Set up production monitoring
4. Configure backup and recovery procedures
5. Document any customizations for your specific business rules
