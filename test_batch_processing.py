"""
Test script for batch processing functionality
Run this to validate the batch processing implementation
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from db import SessionLocal, engine
import models
from batch_processor import BatchProcessor
from scheduler import BatchScheduler

class TestBatchProcessing:
    """Test class for batch processing functionality"""
    
    def setup_method(self):
        """Setup test database session"""
        self.db = SessionLocal()
        self.processor = BatchProcessor()
        self.processor.db = self.db
    
    def teardown_method(self):
        """Cleanup test database session"""
        if self.db:
            self.db.close()
    
    def test_premium_due_calculation_monthly(self):
        """Test premium due calculation for monthly frequency"""
        # Create test policy mandate
        test_policy = models.PolicyMandate(
            policyno="TEST001",
            frequency="monthly",
            premium="1000.00",
            firstdeductiondate="2024-01-15",
            modeofpayment="debit_order"
        )
        
        # Test if premium is due today (mock today as 15th of any month)
        self.processor.batch_date = datetime(2024, 2, 15).date()
        is_due = self.processor.is_premium_due_today(test_policy)
        
        assert is_due == True, "Monthly premium should be due on the 15th"
        
        # Test if premium is not due on different day
        self.processor.batch_date = datetime(2024, 2, 16).date()
        is_due = self.processor.is_premium_due_today(test_policy)
        
        assert is_due == False, "Monthly premium should not be due on the 16th"
    
    def test_premium_due_calculation_quarterly(self):
        """Test premium due calculation for quarterly frequency"""
        test_policy = models.PolicyMandate(
            policyno="TEST002",
            frequency="quarterly",
            premium="3000.00",
            firstdeductiondate="2024-01-15",
            modeofpayment="debit_order"
        )
        
        # Test quarterly due date (3 months later)
        self.processor.batch_date = datetime(2024, 4, 15).date()
        is_due = self.processor.is_premium_due_today(test_policy)
        
        assert is_due == True, "Quarterly premium should be due 3 months later"
        
        # Test non-quarterly date
        self.processor.batch_date = datetime(2024, 3, 15).date()
        is_due = self.processor.is_premium_due_today(test_policy)
        
        assert is_due == False, "Quarterly premium should not be due after 2 months"
    
    def test_suspense_amount_processing(self):
        """Test suspense amount processing logic"""
        policy_no = "TEST003"
        
        # Create test suspense activity
        suspense_activity = models.PolicyActivities(
            policyno=policy_no,
            activitydesc="Suspense Amount",
            remarks="5000.00",
            effectivedate="2024-01-10",
            activitydetails="Suspense Amount",
            datecreated=str(datetime.now()),
            createdby=1
        )
        self.db.add(suspense_activity)
        
        # Create test policy mandate
        policy_mandate = models.PolicyMandate(
            policyno=policy_no,
            frequency="monthly",
            premium="1000.00",
            firstdeductiondate="2024-01-15",
            modeofpayment="debit_order"
        )
        self.db.add(policy_mandate)
        self.db.commit()
        
        # Process premium application
        self.processor.process_premium_application(policy_no)
        
        # Check if premium applied activity was created
        premium_applied = self.db.query(models.PolicyActivities).filter(
            models.PolicyActivities.policyno == policy_no,
            models.PolicyActivities.activitydesc == "Premium Applied"
        ).first()
        
        assert premium_applied is not None, "Premium applied activity should be created"
        assert premium_applied.remarks == "1000.00", "Premium applied amount should match policy premium"
        
        # Check if suspense amount was updated
        updated_suspense = self.db.query(models.PolicyActivities).filter(
            models.PolicyActivities.policyno == policy_no,
            models.PolicyActivities.activitydesc == "Suspense Amount"
        ).order_by(models.PolicyActivities.id.desc()).first()
        
        assert updated_suspense.remarks == "4000.00", "Suspense amount should be reduced by premium amount"
    
    def test_policy_summary_calculation(self):
        """Test policy summary calculation"""
        policy_no = "TEST004"
        
        # Create test activities
        activities = [
            models.PolicyActivities(
                policyno=policy_no,
                activitydesc="Premium Due",
                remarks="1000.00",
                effectivedate="2024-01-15",
                activitydetails="Premium Due",
                datecreated=str(datetime.now()),
                createdby=1
            ),
            models.PolicyActivities(
                policyno=policy_no,
                activitydesc="Premium Due",
                remarks="1000.00",
                effectivedate="2024-02-15",
                activitydetails="Premium Due",
                datecreated=str(datetime.now()),
                createdby=1
            ),
            models.PolicyActivities(
                policyno=policy_no,
                activitydesc="Premium Applied",
                remarks="1000.00",
                effectivedate="2024-01-20",
                activitydetails="Premium Applied",
                datecreated=str(datetime.now()),
                createdby=1
            ),
            models.PolicyActivities(
                policyno=policy_no,
                activitydesc="Money In",
                remarks="2000.00",
                effectivedate="2024-01-18",
                activitydetails="Money In",
                datecreated=str(datetime.now()),
                createdby=1
            )
        ]
        
        for activity in activities:
            self.db.add(activity)
        self.db.commit()
        
        # Update policy summary
        self.processor.update_policy_summary(policy_no)
        
        # Check policy summary
        summary = self.db.query(models.PolicySummary).filter(
            models.PolicySummary.policyno == policy_no
        ).first()
        
        assert summary is not None, "Policy summary should be created"
        assert summary.premiumsdue == "2", "Should have 2 premiums due"
        assert summary.premiumspaid == "1", "Should have 1 premium paid"
        assert summary.totalpremiumspaid == "2000.0", "Total premiums paid should be 2000"
        assert summary.outstanding == "1", "Outstanding should be 1"
    
    def test_policy_status_determination(self):
        """Test policy status determination logic"""
        # Test different outstanding scenarios
        test_cases = [
            (0, "Active"),
            (1, "Grace"),
            (2, "Reinstatement"),
            (4, "Reinstatement"),
            (5, "Lapse"),
            (10, "Lapse")
        ]
        
        for outstanding, expected_status in test_cases:
            policy_no = f"TEST_STATUS_{outstanding}"
            
            # Create policy
            policy = models.Policy(
                policyno=policy_no,
                status="Unknown",
                applicantsigneddate="2024-01-01",
                agentsigneddate="2024-01-01",
                applicationform=True,
                kycform=True,
                mandateform=True
            )
            self.db.add(policy)
            
            # Create policy summary
            summary = models.PolicySummary(
                policyno=policy_no,
                premiumsdue=str(outstanding + 1),
                premiumspaid="1",
                totalpremiumspaid="1000.00",
                outstanding=str(outstanding)
            )
            self.db.add(summary)
            self.db.commit()
            
            # Update policy status
            self.processor.update_individual_policy_status(summary)
            
            # Check updated status
            updated_policy = self.db.query(models.Policy).filter(
                models.Policy.policyno == policy_no
            ).first()
            
            assert updated_policy.status == expected_status, f"Policy with {outstanding} outstanding should have status {expected_status}"

class TestScheduler:
    """Test class for scheduler functionality"""
    
    def test_scheduler_initialization(self):
        """Test scheduler initialization"""
        from db import SQLALCHEMY_DATABASE_URL
        
        scheduler = BatchScheduler(SQLALCHEMY_DATABASE_URL)
        assert scheduler.scheduler is not None, "Scheduler should be initialized"
        
        # Test job scheduling
        scheduler.schedule_daily_batch()
        jobs = scheduler.get_scheduled_jobs()
        
        assert len(jobs) > 0, "Should have at least one scheduled job"
        assert any(job['id'] == 'daily_batch_processing' for job in jobs), "Should have daily batch processing job"

def run_tests():
    """Run all tests"""
    print("Running batch processing tests...")
    
    # Create test instance
    test_batch = TestBatchProcessing()
    test_scheduler = TestScheduler()
    
    try:
        # Setup
        test_batch.setup_method()
        
        # Run batch processing tests
        print("Testing premium due calculation (monthly)...")
        test_batch.test_premium_due_calculation_monthly()
        print("✓ Monthly premium due calculation test passed")
        
        print("Testing premium due calculation (quarterly)...")
        test_batch.test_premium_due_calculation_quarterly()
        print("✓ Quarterly premium due calculation test passed")
        
        print("Testing suspense amount processing...")
        test_batch.test_suspense_amount_processing()
        print("✓ Suspense amount processing test passed")
        
        print("Testing policy summary calculation...")
        test_batch.test_policy_summary_calculation()
        print("✓ Policy summary calculation test passed")
        
        print("Testing policy status determination...")
        test_batch.test_policy_status_determination()
        print("✓ Policy status determination test passed")
        
        # Test scheduler
        print("Testing scheduler initialization...")
        test_scheduler.test_scheduler_initialization()
        print("✓ Scheduler initialization test passed")
        
        print("\n🎉 All tests passed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        raise
    finally:
        # Cleanup
        test_batch.teardown_method()

if __name__ == "__main__":
    run_tests()
