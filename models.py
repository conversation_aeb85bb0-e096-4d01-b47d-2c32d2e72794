# from sqlalchemy.orm import declarative_base
from db import Base
from sqlalchemy import Column, Integer, String, Boolean, Index, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship

# Base = declarative_base()

class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    status = Column(String, index=True)
    viewonly = Column(String, index=True)
    policyadmin = Column(String, index=True)
    servicing = Column(String, index=True)
    mimoadmin = Column(String, index=True)
    sysadmin = Column(String, index=True)
    createdby = Column(String, index=True)
    datecreated = Column(String, index=True)
    hashed_password = Column(String)
    password = Column(String)
    # New user management fields
    failed_login_attempts = Column(Integer, default=0)
    account_locked = Column(Boolean, default=False)
    locked_until = Column(String)
    last_login = Column(String)
    last_password_change = Column(String)
    password_expires_at = Column(String)
    must_change_password = Column(Bo<PERSON>an, default=False)
    created_at = Column(String)
    updated_at = Column(String)
    updated_by = Column(Integer)

    __table_args__ = (Index('ix_depart_id', 'id'),Index('ix_depart_username', 'username'))

# class Beneficiaries(Base):
# id,policyid,title,initials,firstname,middlename,surname,gender,birthdate,maritalstatus,membertype,relationship,benofownership,status,datecreated,createdby
class Beneficiaries(Base):
    __tablename__ = 'beneficiaries'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    policyid = Column(String, index=True)
    policyno = Column(String, index=True)  # Added missing policyno field
    title = Column(String, index=True)
    initials = Column(String, index=True)
    firstname = Column(String, index=True)
    middlename = Column(String, index=True)
    surname = Column(String, index=True)
    gender = Column(String, index=True)
    birthdate = Column(String, index=True)
    maritalstatus = Column(String, index=True)
    membertype = Column(String, index=True)
    relationship = Column(String, index=True)
    benofownership = Column(String, index=True)
    status = Column(String, index=True)
    datecreated = Column(String, index=True)
    createdby = Column(Integer, index=True)
    idnumber = Column(String, index=True)  # Added missing idnumber field
    datejoined = Column(String, index=True)  # New field for date joined

    __table_args__ = (Index('ix_ben_id', 'id'),Index('ix_ben_policyid', 'policyid'))

# class Policy(Base):
# id,policyno,packages,bus,agentcode,agentname,pep,aml,applicantsigneddate,agentsigneddate,applicationform,kycform,mandateform,schemecode,worksitecode,riskprofile,status
class Policy(Base):
    __tablename__ = 'policy'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    policyno = Column(String, index=True)
    packages = Column(String, index=True)
    bus = Column(Boolean, default=False)
    policypayer = Column(String, index=True)
    agentcode = Column(String, index=True)
    agentname = Column(String, index=True)
    pep = Column(Boolean, default=False)
    aml = Column(Boolean, default=False)
    applicantsigneddate = Column(String, index=True, nullable=False)
    agentsigneddate = Column(String, index=True, nullable=False)
    applicationform = Column(Boolean, default=False, nullable=False)
    kycform = Column(Boolean, default=False, nullable=False)
    mandateform = Column(Boolean, default=False, nullable=False)
    schemecode = Column(String, index=True)
    worksitecode = Column(String, index=True)
    riskprofile = Column(String, index=True)
    status = Column(String, index=True)
    datecreated= Column(String, index=True)
    createdby = Column(Integer, index=True)

    __table_args__ = (Index('ix_policy_id', 'id'),Index('ix_policy_policyno', 'policyno'))

# policyholder
# id,policyno,title,initials,middlename,surname,previousname,gender,dateofbirth,countryofbirth,maritalstatus,residentialstatus,primaryid,primaryidexpirydate,secondaryid,secondaryidexpirydate
# ,expatriate,workpermit,workpermitexpirydate,sourceofincome,netmonthlyincome,specifyincome,proofofsourceoffunds,specifyproofofsourceoffunds,occupation,industry,employer,other,commpreference
# ,emailaddress,phoneno,altmobileno,physicaladdressline1,physicaladdressline2,physicaladdressline3,physicaladdressline4,physicaladdressline5city,physicaladdressline6country
# ,permanentaddressvillage,permanentaddressta,permanentaddressdistrict
# ,postaladdressline1,postaladdressline2,postaladdressline3,postaladdressline4,postaladdressline5city,postaladdressline6country
class PolicyHolder(Base):
    __tablename__ = 'policyholder'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    policyno = Column(String, index=True)
    title = Column(String, index=True)
    initials = Column(String, index=True)
    firstname = Column(String, index=True)  # Added missing firstname field
    middlename = Column(String, index=True)
    surname = Column(String, index=True)
    previousname = Column(String, index=True)
    gender = Column(String, index=True)
    dateofbirth = Column(String, index=True)
    countryofbirth = Column(String, index=True)
    maritalstatus = Column(String, index=True)
    residentialstatus = Column(String, index=True)
    primaryid = Column(String, index=True)
    primaryidexpirydate = Column(String, index=True)
    secondaryid = Column(String, index=True)
    secondaryidexpirydate = Column(String, index=True)
    expatriate = Column(String, index=True)
    workpermit = Column(String, index=True)
    workpermitexpirydate = Column(String, index=True)
    sourceofincome = Column(String, index=True)
    netmonthlyincome = Column(String, index=True)
    specifyincome = Column(String, index=True)
    proofofsourceoffunds = Column(String, index=True)
    specifyproofofsourceoffunds = Column(String, index=True)
    occupation = Column(String, index=True)
    industry = Column(String, index=True)
    employer = Column(String, index=True)
    other = Column(String, index=True)
    commpreference = Column(String, index=True)
    emailaddress = Column(String, index=True)
    phoneno = Column(String, index=True)
    altmobileno = Column(String, index=True)
    physicaladdressline1 = Column(String, index=True)
    physicaladdressline2 = Column(String, index=True)
    physicaladdressline3 = Column(String, index=True)
    physicaladdressline4 = Column(String, index=True)
    physicaladdressline5city = Column(String, index=True)
    physicaladdressline6country = Column(String, index=True)
    permanentaddressvillage = Column(String, index=True)
    permanentaddressta = Column(String, index=True)
    permanentaddressdistrict = Column(String, index=True)
    postaladdressline1 = Column(String, index=True)
    postaladdressline2 = Column(String, index=True)
    postaladdressline3 = Column(String, index=True)
    postaladdressline4 = Column(String, index=True)
    postaladdressline5city = Column(String, index=True)
    postaladdressline6country = Column(String, index=True)
    datecreated = Column(String, index=True)  # Added missing datecreated field
    createdby = Column(Integer, index=True)   # Added missing createdby field
    capturedate = Column(String, index=True)  # Added missing capturedate field
    idnumber = Column(String, index=True)     # Added missing idnumber field
    idtype = Column(String, index=True)       # Added missing idtype field
    mobilenumber = Column(String, index=True) # Added missing mobilenumber field
    alternativenumber = Column(String, index=True) # Added missing alternativenumber field
    postalcity = Column(String, index=True)   # Added missing postalcity field
    postalcountrycode = Column(String, index=True) # Added missing postalcountrycode field
    town = Column(String, index=True)         # Added missing town field
    residentialaddressline1 = Column(String, index=True) # Added missing residentialaddressline1 field
    residentialcountrycode = Column(String, index=True)  # Added missing residentialcountrycode field
    residentialdistrict = Column(String, index=True)     # Added missing residentialdistrict field
    residentialvillage = Column(String, index=True)      # Added missing residentialvillage field
    traditionalauthority = Column(String, index=True)    # Added missing traditionalauthority field
    contractsigneddate = Column(String, index=True)      # Added missing contractsigneddate field

    __table_args__ = (Index('ix_policyholder_id', 'id'),Index('ix_policyholder_policyno', 'policyno'))


# class PremiumPayer(Base):
#     __tablename__ = 'premiumpayer'

#     id = Column(Integer, primary_key=True, index=True)
#     policyno = Column(String, index=True)
#     title = Column(String, index=True)
#     initials = Column(String, index=True)
#     middlename = Column(String, index=True)
#     surname = Column(String, index=True)
#     previousname = Column(String, index=True)
#     gender = Column(String, index=True)
#     dateofbirth = Column(String, index=True)
#     countryofbirth = Column(String, index=True)
#     maritalstatus = Column(String, index=True)
#     residentialstatus = Column(String, index=True)
#     primaryid = Column(String, index=True)
#     primaryidexpirydate = Column(String, index=True)
#     secondaryid = Column(String, index=True)
#     secondaryidexpirydate = Column(String, index=True)
#     expatriate = Column(String, index=True)
#     workpermit = Column(String, index=True)
#     workpermitexpirydate = Column(String, index=True)
#     sourceofincome = Column(String, index=True)
#     netmonthlyincome = Column(String, index=True)
#     specifyincome = Column(String, index=True)
#     proofofsourceoffunds = Column(String, index=True)
#     specifyproofofsourceoffunds = Column(String, index=True)
#     occupation = Column(String, index=True)
#     industry = Column(String, index=True)
#     employer = Column(String, index=True)
#     other = Column(String, index=True)
#     commpreference = Column(String, index=True)
#     emailaddress = Column(String, index=True)
#     phoneno = Column(String, index=True)
#     altmobileno = Column(String, index=True)
#     physicaladdressline1 = Column(String, index=True)
#     physicaladdressline2 = Column(String, index=True)
#     physicaladdressline3 = Column(String, index=True)
#     physicaladdressline4 = Column(String, index=True)
#     physicaladdressline5city = Column(String, index=True)
#     physicaladdressline6country = Column(String, index=True)
#     permanentaddressvillage = Column(String, index=True)
#     permanentaddressta = Column(String, index=True)
#     permanentaddressdistrict = Column(String, index=True)
#     postaladdressline1 = Column(String, index=True)
#     postaladdressline2 = Column(String, index=True)
#     postaladdressline3 = Column(String, index=True)
#     postaladdressline4 = Column(String, index=True)
#     postaladdressline5city = Column(String, index=True)
#     postaladdressline6country = Column(String, index=True)

#     __table_args__ = (Index('ix_premiumpayer_id', 'id'),Index('ix_premiumpayer_policyno', 'policyno'))


# policymandate
# id,policyno,modeofpayment,frequency,premium,firstdeductiondate,debitorderFirstname,debitorderLastname,debitorderNameofbank,debitorderBranch,debitorderAccountno,debitorderTypeofaccount 
# ,debitorderSalaryfundingdate,debitorderPremiumonapplication
# ,stoporderEmployeeid,stoporderEmployeename,stoporderEmployername,stoporderEmployeraddress
# ,mobileoperator,mobilephoneno
class PolicyMandate(Base):
    __tablename__ = 'policymandate'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    policyno = Column(String, index=True)
    modeofpayment = Column(String, index=True)
    frequency = Column(String, index=True)
    premium = Column(String, index=True)
    firstdeductiondate = Column(String, index=True)
    debitorderFirstname = Column(String, index=True)
    debitorderLastname = Column(String, index=True)
    debitorderNameofbank = Column(String, index=True)
    debitorderBranch = Column(String, index=True)
    debitorderAccountno = Column(String, index=True)
    debitorderTypeofaccount = Column(String, index=True)
    debitorderSalaryfundingdate = Column(String, index=True)
    debitorderPremiumonapplication = Column(String, index=True)
    stoporderEmployeeid = Column(String, index=True)
    stoporderEmployeename = Column(String, index=True)
    stoporderEmployername = Column(String, index=True)
    stoporderEmployeraddress = Column(String, index=True)
    mobileoperator = Column(String, index=True)
    mobilephoneno = Column(String, index=True)
    datecreated = Column(String, index=True)  # Added missing datecreated field
    createdby = Column(Integer, index=True)   # Added missing createdby field

    __table_args__ = (Index('ix_policymandate_id', 'id'), Index('ix_policymandate_policyno', 'policyno'))

# policysummary
# id,policyno,premiumsdue,premiumspaid,totalpremiumspaid,outstanding 
class PolicySummary(Base):
    __tablename__ = 'policysummary'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    policyno = Column(String, index=True)
    premiumsdue = Column(String, index=True)
    premiumspaid = Column(String, index=True)
    totalpremiumspaid = Column(String, index=True)
    outstanding = Column(String, index=True)

    __table_args__ = (Index('ix_policysummary_id', 'id'), Index('ix_policysummary_policyno', 'policyno'))

class PremiumPayer(Base):
    __tablename__ = 'premiumpayer'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    policyno = Column(String, index=True)
    title = Column(String, index=True)
    initials = Column(String, index=True)
    middlename = Column(String, index=True)
    surname = Column(String, index=True)
    previousname = Column(String, index=True)
    gender = Column(String, index=True)
    dateofbirth = Column(String, index=True)
    countryofbirth = Column(String, index=True)
    maritalstatus = Column(String, index=True)
    residentialstatus = Column(String, index=True)
    primaryid = Column(String, index=True)
    primaryidexpirydate = Column(String, index=True)
    secondaryid = Column(String, index=True)
    secondaryidexpirydate = Column(String, index=True)
    expatriate = Column(String, index=True)
    workpermit = Column(String, index=True)
    workpermitexpirydate = Column(String, index=True)
    sourceofincome = Column(String, index=True)
    netmonthlyincome = Column(String, index=True)
    specifyincome = Column(String, index=True)
    proofofsourceoffunds = Column(String, index=True)
    specifyproofofsourceoffunds = Column(String, index=True)
    occupation = Column(String, index=True)
    industry = Column(String, index=True)
    employer = Column(String, index=True)
    other = Column(String, index=True)
    commpreference = Column(String, index=True)
    emailaddress = Column(String, index=True)
    phoneno = Column(String, index=True)
    altmobileno = Column(String, index=True)
    physicaladdressline1 = Column(String, index=True)
    physicaladdressline2 = Column(String, index=True)
    physicaladdressline3 = Column(String, index=True)
    physicaladdressline4 = Column(String, index=True)
    physicaladdressline5city = Column(String, index=True)
    physicaladdressline6country = Column(String, index=True)
    permanentaddressvillage = Column(String, index=True)
    permanentaddressta = Column(String, index=True)
    permanentaddressdistrict = Column(String, index=True)
    postaladdressline1 = Column(String, index=True)
    postaladdressline2 = Column(String, index=True)
    postaladdressline3 = Column(String, index=True)
    postaladdressline4 = Column(String, index=True)
    postaladdressline5city = Column(String, index=True)
    postaladdressline6country = Column(String, index=True)

    __table_args__ = (Index('ix_premiumpayer_id', 'id'),Index('ix_premiumpayer_policyno', 'policyno'))

# policyactivities
# id,policyid,activitydesc,remarks,status,datecreated,createdby,activitydetails,effectivedate
class PolicyActivities(Base):
    __tablename__ = 'policyactivities'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    policyid = Column(String, index=True)
    policyno = Column(String, index=True)
    activitydesc = Column(String, index=True)
    remarks = Column(String, index=True)
    status = Column(String, index=True)
    datecreated = Column(String, index=True)
    createdby = Column(Integer, index=True)
    activitydetails = Column(String, index=True)
    effectivedate = Column(String, index=True)

    __table_args__ = (Index('ix_policyactivities_id', 'id'), Index('ix_policyactivities_policyid', 'policyid'), Index('ix_policyactivities_policyno', 'policyno'))

# Master Data Models

class Agents(Base):
    __tablename__ = 'agents'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    agentcode = Column(String, index=True, unique=True)
    agentname = Column(String, index=True)
    email = Column(String, index=True)
    marketsegment = Column(String, index=True)
    datecreated = Column(String, index=True)
    createdby = Column(Integer, index=True)

    __table_args__ = (Index('ix_agents_id', 'id'), Index('ix_agents_agentcode', 'agentcode'))

# MIMO Module Models

# Financial Transactions Header Table
class FinancialTransactions(Base):
    __tablename__ = 'financial_transactions'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    transaction_number = Column(String, unique=True, index=True)  # TXN-YYYYMMDD-NNNN
    transaction_date = Column(String, index=True)
    transaction_type = Column(String, index=True)  # "Money In", "Money Out", "Reversal"
    total_amount = Column(String, index=True)  # Sum of all line items
    currency = Column(String, default="MWK")
    batch_reference = Column(String, index=True)  # For file uploads
    status = Column(String, index=True, default="Pending")  # "Pending", "Approved", "Rejected", "Reversed"
    created_by = Column(Integer, index=True)
    created_date = Column(String, index=True)
    approved_by = Column(Integer, index=True)
    approved_date = Column(String, index=True)
    remarks = Column(String)  # Transaction-level notes

    __table_args__ = (
        Index('ix_financial_transactions_id', 'id'),
        Index('ix_financial_transactions_transaction_number', 'transaction_number'),
        Index('ix_financial_transactions_status', 'status'),
        Index('ix_financial_transactions_transaction_date', 'transaction_date')
    )

# Financial Transaction Details Table
class FinancialTransactionDetails(Base):
    __tablename__ = 'financial_transaction_details'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    transaction_id = Column(Integer, index=True)  # Foreign Key to financial_transactions
    line_number = Column(Integer, index=True)  # 1, 2, 3... for each transaction
    policy_no = Column(String, index=True)
    activity_desc = Column(String, index=True)  # "Money In", "Reverse Money In"
    amount = Column(String, index=True)
    effective_date = Column(String, index=True)
    activity_details = Column(String)
    remarks = Column(String)  # Line-level notes
    policy_activity_id = Column(Integer, index=True)  # Links to created PolicyActivity
    paypoint_name = Column(String, index=True)  # Paypoint name for the transaction
    paypoint_account = Column(String, index=True)  # Paypoint account number

    __table_args__ = (
        Index('ix_financial_transaction_details_id', 'id'),
        Index('ix_financial_transaction_details_transaction_id', 'transaction_id'),
        Index('ix_financial_transaction_details_policy_no', 'policy_no'),
        Index('ix_financial_transaction_details_paypoint_name', 'paypoint_name'),
        Index('ix_financial_transaction_details_paypoint_account', 'paypoint_account')
    )

class WorksiteCodes(Base):
    __tablename__ = 'worksitecodes'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    worksitename = Column(String, index=True)
    worksitecode = Column(String, index=True, unique=True)
    datecreated = Column(String, index=True)
    createdby = Column(Integer, index=True)

    __table_args__ = (Index('ix_worksitecodes_id', 'id'), Index('ix_worksitecodes_worksitecode', 'worksitecode'))

class Countries(Base):
    __tablename__ = 'countries'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    country = Column(String, index=True, unique=True)
    datecreated = Column(String, index=True)
    createdby = Column(Integer, index=True)

    __table_args__ = (Index('ix_countries_id', 'id'), Index('ix_countries_country', 'country'))

class Districts(Base):
    __tablename__ = 'districts'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    district = Column(String, index=True)
    country = Column(String, index=True)
    datecreated = Column(String, index=True)
    createdby = Column(Integer, index=True)

    __table_args__ = (Index('ix_districts_id', 'id'), Index('ix_districts_district', 'district'))

class PayPoints(Base):
    __tablename__ = 'paypoints'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    paypointcode = Column(String, index=True, unique=True)
    paypointname = Column(String, index=True)
    paymentmethod = Column(String, index=True)
    datecreated = Column(String, index=True)
    createdby = Column(Integer, index=True)

    __table_args__ = (Index('ix_paypoints_id', 'id'), Index('ix_paypoints_paypointcode', 'paypointcode'))

class BankBranches(Base):
    __tablename__ = 'bankbranches'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    branchname = Column(String, index=True, unique=True)
    datecreated = Column(String, index=True)
    createdby = Column(Integer, index=True)

    __table_args__ = (Index('ix_bankbranches_id', 'id'), Index('ix_bankbranches_branchname', 'branchname'))

class Relationships(Base):
    __tablename__ = 'relationships'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    relationship = Column(String, index=True, unique=True)
    type = Column(String, index=True)  # New column for relationship type
    datecreated = Column(String, index=True)
    createdby = Column(Integer, index=True)

    __table_args__ = (Index('ix_relationships_id', 'id'), Index('ix_relationships_relationship', 'relationship'), Index('ix_relationships_type', 'type'))

class MthunziDocuments(Base):
    __tablename__ = "mthunzidocuments"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    policynumber = Column(String, index=True, nullable=False)
    firstname = Column(String, index=True, nullable=False)
    surname = Column(String, index=True, nullable=False)
    initials = Column(String, index=True, nullable=False)
    idnumber = Column(String, index=True, nullable=False)
    premium = Column(String, index=True, nullable=False)
    producttype = Column(String, index=True, nullable=False)
    mandatedate = Column(String, index=True, nullable=False)
    agentcode = Column(String, index=True, nullable=False)
    agentname = Column(String, index=True, nullable=False)
    paypointid = Column(String, index=True, nullable=False)
    signeddate = Column(String, index=True, nullable=False)
    datecreated = Column(String, index=True, nullable=False)
    createdby = Column(Integer, index=True, nullable=False)

    # Relationship to document details
    document_details = relationship("DocumentDetails", back_populates="document_header")

    __table_args__ = (Index('ix_mthunzidocuments_id', 'id'), Index('ix_mthunzidocuments_policynumber', 'policynumber'), Index('ix_mthunzidocuments_agentcode', 'agentcode'))


class DocumentDetails(Base):
    __tablename__ = "documentdetails"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    document_header_id = Column(Integer, ForeignKey("mthunzidocuments.id"), nullable=False, index=True)
    policyno = Column(String, index=True, nullable=False)
    documentfolder = Column(String, index=True, nullable=False)
    filename = Column(String, index=True, nullable=False)
    filepath = Column(String, index=True, nullable=False)
    remarks = Column(String, index=True, nullable=False)
    datecreated = Column(String, index=True, nullable=False)
    createdby = Column(Integer, index=True, nullable=False)

    # Relationship to document header
    document_header = relationship("MthunziDocuments", back_populates="document_details")

    __table_args__ = (Index('ix_documentdetails_id', 'id'), Index('ix_documentdetails_document_header_id', 'document_header_id'), Index('ix_documentdetails_policyno', 'policyno'), Index('ix_documentdetails_remarks', 'remarks'))


# Premium Rate Tables

class FamilyRates(Base):
    __tablename__ = "familyrates"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    package = Column(String, index=True, nullable=False)
    familytype = Column(String, index=True, nullable=False)
    bus = Column(String, index=True, nullable=False)
    rate = Column(String, index=True, nullable=False)
    effectivedate = Column(String, index=True, nullable=False)
    datecreated = Column(String, index=True, nullable=False)
    createdby = Column(Integer, index=True, nullable=False)

    __table_args__ = (Index('ix_familyrates_id', 'id'), Index('ix_familyrates_package', 'package'), Index('ix_familyrates_familytype', 'familytype'), Index('ix_familyrates_bus', 'bus'))


class BeneficiaryRates(Base):
    __tablename__ = "beneficiaryrates"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    package = Column(String, index=True, nullable=False)
    dependenttype = Column(String, index=True, nullable=False)
    bus = Column(String, index=True, nullable=False)
    minage = Column(Integer, index=True, nullable=False)
    maxage = Column(Integer, index=True, nullable=False)
    rate = Column(String, index=True, nullable=False)
    effectivedate = Column(String, index=True, nullable=False)
    datecreated = Column(String, index=True, nullable=False)
    createdby = Column(Integer, index=True, nullable=False)

    __table_args__ = (Index('ix_beneficiaryrates_id', 'id'), Index('ix_beneficiaryrates_package', 'package'), Index('ix_beneficiaryrates_dependenttype', 'dependenttype'), Index('ix_beneficiaryrates_bus', 'bus'))


# User Management Models

class UserRole(Base):
    __tablename__ = 'user_roles'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    role_name = Column(String, unique=True, nullable=False, index=True)
    role_description = Column(String)
    is_active = Column(Boolean, default=True)
    created_by = Column(Integer)
    date_created = Column(String)
    modified_by = Column(Integer)
    date_modified = Column(String)

    __table_args__ = (Index('ix_user_roles_id', 'id'), Index('ix_user_roles_name', 'role_name'))


class Permission(Base):
    __tablename__ = 'permissions'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    # Atomic role key, e.g., 'policies.view' or 'premium-rates.delete'
    permission_name = Column(String, unique=True, nullable=False, index=True)
    permission_description = Column(String)
    # Resource and action provide first-class columns for enforcement and UI grouping
    resource = Column(String, index=True)  # e.g., 'policies', 'documents', 'user-management'
    action = Column(String, index=True)    # e.g., 'create', 'update', 'view', 'delete'
    is_active = Column(Boolean, default=True)
    created_by = Column(Integer)
    date_created = Column(String)

    __table_args__ = (
        Index('ix_permissions_id', 'id'),
        Index('ix_permissions_name', 'permission_name'),
        Index('ix_permissions_resource', 'resource'),
        Index('ix_permissions_action', 'action'),
        UniqueConstraint('resource', 'action', name='uq_permissions_resource_action'),
    )


class RolePermission(Base):
    __tablename__ = 'role_permissions'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    role_id = Column(Integer, ForeignKey('user_roles.id', ondelete='CASCADE'), nullable=False)
    permission_id = Column(Integer, ForeignKey('permissions.id', ondelete='CASCADE'), nullable=False)
    granted_by = Column(Integer)
    date_granted = Column(String)

    __table_args__ = (Index('ix_role_permissions_id', 'id'), Index('ix_role_permissions_role', 'role_id'), Index('ix_role_permissions_permission', 'permission_id'))


class UserRoleAssignment(Base):
    __tablename__ = 'user_role_assignments'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    role_id = Column(Integer, ForeignKey('user_roles.id', ondelete='CASCADE'), nullable=False)
    assigned_by = Column(Integer)
    date_assigned = Column(String)
    is_active = Column(Boolean, default=True)

    __table_args__ = (Index('ix_user_role_assignments_id', 'id'), Index('ix_user_role_assignments_user', 'user_id'), Index('ix_user_role_assignments_role', 'role_id'))


class UserPermissionAssignment(Base):
    __tablename__ = 'user_permission_assignments'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    permission_id = Column(Integer, ForeignKey('permissions.id', ondelete='CASCADE'), nullable=False)
    assigned_by = Column(Integer)
    date_assigned = Column(String)
    is_active = Column(Boolean, default=True)

    __table_args__ = (
        Index('ix_user_permission_assignments_id', 'id'),
        Index('ix_user_permission_assignments_user', 'user_id'),
        Index('ix_user_permission_assignments_permission', 'permission_id'),
        UniqueConstraint('user_id', 'permission_id', name='uq_user_permission_assignment')
    )


class UserAuditLog(Base):
    __tablename__ = 'user_audit_logs'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='SET NULL'))
    username = Column(String, index=True)
    activity_type = Column(String, nullable=False, index=True)  # LOGIN, LOGOUT, CREATE, UPDATE, DELETE, etc.
    activity_description = Column(String)
    ip_address = Column(String)
    user_agent = Column(String)
    session_id = Column(String)
    resource_accessed = Column(String)
    old_values = Column(String)  # JSON string
    new_values = Column(String)  # JSON string
    status = Column(String, default='SUCCESS')  # SUCCESS, FAILED, BLOCKED
    date_created = Column(String, index=True)

    __table_args__ = (Index('ix_user_audit_logs_id', 'id'), Index('ix_user_audit_logs_user', 'user_id'), Index('ix_user_audit_logs_activity', 'activity_type'), Index('ix_user_audit_logs_date', 'date_created'))


class UserSession(Base):
    __tablename__ = 'user_sessions'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    session_token = Column(String, nullable=False, index=True)
    ip_address = Column(String)
    user_agent = Column(String)
    login_time = Column(String)
    last_activity = Column(String)
    logout_time = Column(String)
    is_active = Column(Boolean, default=True)
    expires_at = Column(String)

    __table_args__ = (Index('ix_user_sessions_id', 'id'), Index('ix_user_sessions_user', 'user_id'), Index('ix_user_sessions_token', 'session_token'))


class PasswordResetToken(Base):
    __tablename__ = 'password_reset_tokens'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    token = Column(String, nullable=False)
    expires_at = Column(String, nullable=False)
    used = Column(Boolean, default=False)
    created_at = Column(String)

    __table_args__ = (Index('ix_password_reset_tokens_id', 'id'), Index('ix_password_reset_tokens_user', 'user_id'), Index('ix_password_reset_tokens_token', 'token'))


class LoginAttempt(Base):
    __tablename__ = 'login_attempts'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    username = Column(String, index=True)
    ip_address = Column(String, index=True)
    user_agent = Column(String)
    attempt_time = Column(String)
    status = Column(String)  # SUCCESS, FAILED, BLOCKED
    failure_reason = Column(String)
    session_id = Column(String)

    __table_args__ = (Index('ix_login_attempts_id', 'id'), Index('ix_login_attempts_username', 'username'), Index('ix_login_attempts_ip', 'ip_address'), Index('ix_login_attempts_time', 'attempt_time'))