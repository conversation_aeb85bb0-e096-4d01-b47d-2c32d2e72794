from pydantic import BaseModel
# from sqlalchemy
import uuid
from typing import Optional, List

# Generate a random UUID (Version 4)
generated_uuid = uuid.uuid4()

# Print the UUID object
# print(generated_uuid)

# Convert the UUID object to a string
uuid_string = str(generated_uuid)
# print(f"UUID as string: {uuid_string}")

# Access the hexadecimal representation without hyphens
uuid_hex = generated_uuid.hex

# UserBase schema
class UserBase(BaseModel):
    id: Optional[int] = None
    username: str
    email: str
    status: str
    viewonly: Optional[str] = None
    policyadmin: Optional[str] = None
    servicing: Optional[str] = None
    mimoadmin: Optional[str] = None
    sysadmin: Optional[str] = None

# UserCreate schema 
class UserCreate(BaseModel):
    id: Optional[int] = None
    username: str
    email: str
    password: str
    status: str
    viewonly: Optional[str] = None
    policyadmin: Optional[str] = None
    servicing: Optional[str] = None
    mimoadmin: Optional[str] = None
    sysadmin: Optional[str] = None

class Userlogin(BaseModel):
    username: str
    password: str

# Policy
class Policy(BaseModel):
    id: Optional[int] = None
    policyno: Optional[str] = None
    packages: Optional[str] = None
    bus: Optional[bool] = None
    policypayer: Optional[str] = None
    agentcode: Optional[str] = None
    agentname: Optional[str] = None
    pep: Optional[bool] = None
    aml: Optional[bool] = None
    applicantsigneddate: Optional[str] = None
    agentsigneddate: Optional[str] = None
    applicationform: Optional[bool] = None
    kycform: Optional[bool] = None
    mandateform: Optional[bool] = None
    schemecode: Optional[str] = None
    worksitecode: Optional[str] = None
    riskprofile: Optional[str] = None
    status: Optional[str] = None
    datecreated: Optional[str] = None
    createdby: Optional[int] = None

    class Config:
        from_attributes = True  # Updated for Pydantic v2


# policyholder
# id,policyno,title,initials,middlename,surname,previousname,gender,dateofbirth,countryofbirth,maritalstatus,residentialstatus,primaryid,primaryidexpirydate,secondaryid,secondaryidexpirydate
# ,expatriate,workpermit,workpermitexpirydate,sourceofincome,netmonthlyincome,specifyincome,proofofsourceoffunds,specifyproofofsourceoffunds,occupation,industry,employer,other,commpreference
# ,emailaddress,phoneno,altmobileno,physicaladdressline1,physicaladdressline2,physicaladdressline3,physicaladdressline4,physicaladdressline5city,physicaladdressline6country
# ,permanentaddressvillage,permanentaddressta,permanentaddressdistrict
# ,postaladdressline1,postaladdressline2,postaladdressline3,postaladdressline4,postaladdressline5city,postaladdressline6country
class PolicyHolder(BaseModel):
    id: Optional[int] = None
    policyno: Optional[str] = None
    title: Optional[str] = None
    initials: Optional[str] = None
    firstname: Optional[str] = None  # Added missing field
    middlename: Optional[str] = None
    surname: Optional[str] = None
    previousname: Optional[str] = None
    gender: Optional[str] = None
    dateofbirth: Optional[str] = None
    countryofbirth: Optional[str] = None
    maritalstatus: Optional[str] = None
    residentialstatus: Optional[str] = None
    primaryid: Optional[str] = None
    primaryidexpirydate: Optional[str] = None
    secondaryid: Optional[str] = None
    secondaryidexpirydate: Optional[str] = None
    expatriate: Optional[str] = None
    workpermit: Optional[str] = None
    workpermitexpirydate: Optional[str] = None
    sourceofincome: Optional[str] = None
    netmonthlyincome: Optional[str] = None
    specifyincome: Optional[str] = None
    proofofsourceoffunds: Optional[str] = None
    specifyproofofsourceoffunds: Optional[str] = None
    occupation: Optional[str] = None
    industry: Optional[str] = None
    employer: Optional[str] = None
    other: Optional[str] = None
    commpreference: Optional[str] = None
    emailaddress: Optional[str] = None
    phoneno: Optional[str] = None
    altmobileno: Optional[str] = None
    physicaladdressline1: Optional[str] = None
    physicaladdressline2: Optional[str] = None
    physicaladdressline3: Optional[str] = None
    physicaladdressline4: Optional[str] = None
    physicaladdressline5city: Optional[str] = None
    physicaladdressline6country: Optional[str] = None
    permanentaddressvillage: Optional[str] = None
    permanentaddressta: Optional[str] = None
    permanentaddressdistrict: Optional[str] = None
    postaladdressline1: Optional[str] = None
    postaladdressline2: Optional[str] = None
    postaladdressline3: Optional[str] = None
    postaladdressline4: Optional[str] = None
    postaladdressline5city: Optional[str] = None
    postaladdressline6country: Optional[str] = None
    datecreated: Optional[str] = None  # Added missing field
    createdby: Optional[int] = None   # Added missing field
    capturedate: Optional[str] = None  # Added missing field
    idnumber: Optional[str] = None     # Added missing field
    idtype: Optional[str] = None       # Added missing field
    mobilenumber: Optional[str] = None # Added missing field
    alternativenumber: Optional[str] = None # Added missing field
    postalcity: Optional[str] = None   # Added missing field
    postalcountrycode: Optional[str] = None # Added missing field
    town: Optional[str] = None         # Added missing field
    residentialaddressline1: Optional[str] = None # Added missing field
    residentialcountrycode: Optional[str] = None  # Added missing field
    residentialdistrict: Optional[str] = None     # Added missing field
    residentialvillage: Optional[str] = None      # Added missing field
    traditionalauthority: Optional[str] = None    # Added missing field
    contractsigneddate: Optional[str] = None      # Added missing field

    class Config:
        orm_mode = True

# PremiumPayer schema
class PremiumPayer(BaseModel):
    id: Optional[int] = None
    policyno: str
    title: str
    initials: str
    middlename: Optional[str] = None
    surname: str
    previousname: Optional[str] = None
    gender: str
    dateofbirth: str
    countryofbirth: str
    maritalstatus: str
    residentialstatus: str
    primaryid: str
    primaryidexpirydate: str
    secondaryid: Optional[str] = None
    secondaryidexpirydate: Optional[str] = None
    expatriate: Optional[str] = None
    workpermit: Optional[str] = None
    workpermitexpirydate: Optional[str] = None
    sourceofincome: Optional[str] = None
    netmonthlyincome: Optional[str] = None
    specifyincome: Optional[str] = None
    proofofsourceoffunds: Optional[str] = None
    specifyproofofsourceoffunds: Optional[str] = None
    occupation: Optional[str] = None
    industry: Optional[str] = None
    employer: Optional[str] = None
    other: Optional[str] = None
    commpreference: Optional[str] = None
    emailaddress: Optional[str] = None
    phoneno: Optional[str] = None
    altmobileno: Optional[str] = None
    physicaladdressline1: Optional[str] = None
    physicaladdressline2: Optional[str] = None
    physicaladdressline3: Optional[str] = None
    physicaladdressline4: Optional[str] = None
    physicaladdressline5city: Optional[str] = None
    physicaladdressline6country: Optional[str] = None
    permanentaddressvillage: Optional[str] = None
    permanentaddressta: Optional[str] = None
    permanentaddressdistrict: Optional[str] = None
    postaladdressline1: Optional[str] = None
    postaladdressline2: Optional[str] = None
    postaladdressline3: Optional[str] = None
    postaladdressline4: Optional[str] = None
    postaladdressline5city: Optional[str] = None
    postaladdressline6country: Optional[str] = None

    class Config:
        orm_mode = True

# PolicyMandate schema
class PolicyMandate(BaseModel):
    id: Optional[int] = None
    policyno: str
    modeofpayment: str
    frequency: str
    premium: str
    firstdeductiondate: str
    debitorderFirstname: Optional[str] = None
    debitorderLastname: Optional[str] = None
    debitorderNameofbank: Optional[str] = None
    debitorderBranch: Optional[str] = None
    debitorderAccountno: Optional[str] = None
    debitorderTypeofaccount: Optional[str] = None
    debitorderSalaryfundingdate: Optional[str] = None
    debitorderPremiumonapplication: Optional[str] = None
    stoporderEmployeeid: Optional[str] = None
    stoporderEmployeename: Optional[str] = None
    stoporderEmployername: Optional[str] = None
    stoporderEmployeraddress: Optional[str] = None
    mobileoperator: Optional[str] = None
    mobilephoneno: Optional[str] = None

    class Config:
        orm_mode = True

# PolicySummary schema
class PolicySummary(BaseModel):
    id: Optional[int] = None
    policyno: str
    premiumsdue: str
    premiumspaid: str
    totalpremiumspaid: str
    outstanding: str

    class Config:
        orm_mode = True

# PolicyActivities schema
class PolicyActivitiesBase(BaseModel):
    policyno: str
    activitydesc: str
    remarks: Optional[str] = None  # Made optional
    effectivedate: str
    activitydetails: str  # Made required

class PolicyActivitiesCreate(PolicyActivitiesBase):
    datecreated: Optional[str] = None
    createdby: Optional[int] = None

class PolicyActivitiesUpdate(PolicyActivitiesBase):
    policyno: Optional[str] = None
    activitydesc: Optional[str] = None
    remarks: Optional[str] = None  # Already optional, consistent with base
    effectivedate: Optional[str] = None
    activitydetails: Optional[str] = None  # Optional for updates

class PolicyActivities(PolicyActivitiesBase):
    id: Optional[int] = None
    policyid: Optional[str] = None
    status: Optional[str] = None
    datecreated: Optional[str] = None
    createdby: Optional[int] = None

    class Config:
        from_attributes = True  # Updated for Pydantic v2

# MIMO Module Schemas

# Financial Transaction Detail schemas
class FinancialTransactionDetailBase(BaseModel):
    policy_no: str
    activity_desc: str  # "Money In", "Reverse Money In"
    amount: str
    effective_date: str
    activity_details: str
    remarks: Optional[str] = None
    paypoint_name: Optional[str] = None
    paypoint_account: Optional[str] = None

class FinancialTransactionDetailCreate(FinancialTransactionDetailBase):
    line_number: Optional[int] = None

class FinancialTransactionDetailUpdate(BaseModel):
    policy_no: Optional[str] = None
    activity_desc: Optional[str] = None
    amount: Optional[str] = None
    effective_date: Optional[str] = None
    activity_details: Optional[str] = None
    remarks: Optional[str] = None
    paypoint_name: Optional[str] = None
    paypoint_account: Optional[str] = None

class FinancialTransactionDetail(FinancialTransactionDetailBase):
    id: Optional[int] = None
    transaction_id: Optional[int] = None
    line_number: Optional[int] = None
    policy_activity_id: Optional[int] = None

    class Config:
        from_attributes = True

# Financial Transaction Header schemas
class FinancialTransactionBase(BaseModel):
    transaction_date: str
    transaction_type: str  # "Money In", "Money Out", "Reversal"
    batch_reference: Optional[str] = None
    remarks: Optional[str] = None

class FinancialTransactionCreate(FinancialTransactionBase):
    details: List[FinancialTransactionDetailCreate]

class FinancialTransactionUpdate(BaseModel):
    transaction_date: Optional[str] = None
    transaction_type: Optional[str] = None
    batch_reference: Optional[str] = None
    remarks: Optional[str] = None
    status: Optional[str] = None
    approved_by: Optional[int] = None
    approved_date: Optional[str] = None

class FinancialTransaction(FinancialTransactionBase):
    id: Optional[int] = None
    transaction_number: Optional[str] = None
    total_amount: Optional[str] = None
    currency: Optional[str] = None
    status: Optional[str] = None
    created_by: Optional[int] = None
    created_date: Optional[str] = None
    approved_by: Optional[int] = None
    approved_date: Optional[str] = None
    details: Optional[List[FinancialTransactionDetail]] = None

    class Config:
        from_attributes = True

# MIMO specific request/response schemas
class MIMOTransactionRequest(BaseModel):
    transaction_type: str
    transaction_date: str
    batch_reference: Optional[str] = None
    remarks: Optional[str] = None
    transactions: List[FinancialTransactionDetailCreate]

class MIMOTransactionResponse(BaseModel):
    success: bool
    message: str
    transaction_id: Optional[int] = None
    transaction_number: Optional[str] = None
    total_amount: Optional[str] = None

class MIMOSearchRequest(BaseModel):
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    transaction_type: Optional[str] = None
    status: Optional[str] = None
    policy_no: Optional[str] = None
    transaction_number: Optional[str] = None




# Beneficiaries schema
class BeneficiariesBase(BaseModel):
    policyno: str
    title: str
    initials: str
    firstname: str
    surname: str
    gender: str
    birthdate: str
    membertype: str
    relationship: str
    idnumber: Optional[str] = None
    maritalstatus: Optional[str] = None

class BeneficiariesCreate(BeneficiariesBase):
    datejoined: Optional[str] = None
    datecreated: Optional[str] = None
    createdby: Optional[int] = None

class BeneficiariesUpdate(BaseModel):
    title: Optional[str] = None
    initials: Optional[str] = None
    firstname: Optional[str] = None
    surname: Optional[str] = None
    gender: Optional[str] = None
    birthdate: Optional[str] = None
    membertype: Optional[str] = None
    relationship: Optional[str] = None
    idnumber: Optional[str] = None
    maritalstatus: Optional[str] = None
    datejoined: Optional[str] = None

class Beneficiaries(BeneficiariesBase):
    id: Optional[int] = None
    policyid: Optional[str] = None
    middlename: Optional[str] = None
    benofownership: Optional[str] = None
    status: Optional[str] = None
    datecreated: Optional[str] = None
    createdby: Optional[int] = None
    datejoined: Optional[str] = None

    class Config:
        orm_mode = True

class UserInDB(UserCreate):
    hashed_password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None
    class Config:
        # orm_mode = True
        from_attributes = True

class PasswordChange(BaseModel):
    new_password: str

class UserUpdate(BaseModel):
    username: str
    email: str
    status: str
    viewonly: Optional[str] = None
    policyadmin: Optional[str] = None
    servicing: Optional[str] = None
    mimoadmin: Optional[str] = None
    sysadmin: Optional[str] = None

# Master Data Schemas

class AgentsBase(BaseModel):
    id: Optional[int] = None
    agentcode: str
    agentname: str
    email: Optional[str] = None
    marketsegment: Optional[str] = None
    datecreated: Optional[str] = None
    createdby: Optional[int] = None

    class Config:
        orm_mode = True

class AgentsCreate(AgentsBase):
    pass

class AgentsUpdate(BaseModel):
    agentcode: Optional[str] = None
    agentname: Optional[str] = None
    email: Optional[str] = None
    marketsegment: Optional[str] = None

class WorksiteCodesBase(BaseModel):
    id: Optional[int] = None
    worksitename: str
    worksitecode: str
    datecreated: Optional[str] = None
    createdby: Optional[int] = None

    class Config:
        orm_mode = True

class WorksiteCodesCreate(WorksiteCodesBase):
    pass

class WorksiteCodesUpdate(BaseModel):
    worksitename: Optional[str] = None
    worksitecode: Optional[str] = None

class CountriesBase(BaseModel):
    id: Optional[int] = None
    country: str
    datecreated: Optional[str] = None
    createdby: Optional[int] = None

    class Config:
        orm_mode = True

class CountriesCreate(CountriesBase):
    pass

class CountriesUpdate(BaseModel):
    country: Optional[str] = None

class DistrictsBase(BaseModel):
    id: Optional[int] = None
    district: str
    country: str
    datecreated: Optional[str] = None
    createdby: Optional[int] = None

    class Config:
        orm_mode = True

class DistrictsCreate(DistrictsBase):
    pass

class DistrictsUpdate(BaseModel):
    district: Optional[str] = None
    country: Optional[str] = None

class PayPointsBase(BaseModel):
    id: Optional[int] = None
    paypointcode: str
    paypointname: str
    paymentmethod: str
    datecreated: Optional[str] = None
    createdby: Optional[int] = None

    class Config:
        orm_mode = True

class PayPointsCreate(PayPointsBase):
    pass

class PayPointsUpdate(BaseModel):
    paypointcode: Optional[str] = None
    paypointname: Optional[str] = None
    paymentmethod: Optional[str] = None

class BankBranchesBase(BaseModel):
    id: Optional[int] = None
    branchname: str
    datecreated: Optional[str] = None
    createdby: Optional[int] = None

    class Config:
        orm_mode = True

class BankBranchesCreate(BankBranchesBase):
    pass

class BankBranchesUpdate(BaseModel):
    branchname: Optional[str] = None

class RelationshipsBase(BaseModel):
    id: Optional[int] = None
    relationship: str
    type: Optional[str] = None  # New field for relationship type
    datecreated: Optional[str] = None
    createdby: Optional[int] = None

    class Config:
        from_attributes = True  # Updated for Pydantic v2

class RelationshipsCreate(RelationshipsBase):
    pass

class RelationshipsUpdate(BaseModel):
    relationship: Optional[str] = None
    type: Optional[str] = None  # New field for relationship type

# Premium Rate Schemas

class FamilyRatesBase(BaseModel):
    id: Optional[int] = None
    package: str
    familytype: str
    bus: bool
    rate: str
    effectivedate: str
    datecreated: Optional[str] = None
    createdby: Optional[int] = None

    class Config:
        orm_mode = True

class FamilyRatesCreate(FamilyRatesBase):
    pass

class FamilyRatesUpdate(BaseModel):
    package: Optional[str] = None
    familytype: Optional[str] = None
    bus: Optional[bool] = None
    rate: Optional[str] = None
    effectivedate: Optional[str] = None

class BeneficiaryRatesBase(BaseModel):
    id: Optional[int] = None
    package: str
    dependenttype: str
    bus: bool
    minage: int
    maxage: int
    rate: str
    effectivedate: str
    datecreated: Optional[str] = None
    createdby: Optional[int] = None

    class Config:
        orm_mode = True

class BeneficiaryRatesCreate(BeneficiaryRatesBase):
    pass

class BeneficiaryRatesUpdate(BaseModel):
    package: Optional[str] = None
    dependenttype: Optional[str] = None
    bus: Optional[bool] = None
    minage: Optional[int] = None
    maxage: Optional[int] = None
    rate: Optional[str] = None
    effectivedate: Optional[str] = None

# Premium Calculation Schemas

class BeneficiaryForCalculation(BaseModel):
    membertype: str
    birthdate: str
    relationship: str

class PremiumCalculationRequest(BaseModel):
    package: str
    bus_status: bool
    beneficiaries: List[BeneficiaryForCalculation]

class PremiumCalculationResponse(BaseModel):
    status: str
    calculation: dict
    calculated_by: int
    calculated_at: str