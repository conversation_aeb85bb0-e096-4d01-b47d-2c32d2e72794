"""
Scheduler Module for Batch Processing
Integrates APScheduler with FastAPI to run daily batch processing at 1 AM
"""

import logging
from datetime import datetime
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.pool import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
import asyncio
from batch_processor import BatchProcessor

# Configure logging
logger = logging.getLogger(__name__)

class BatchScheduler:
    """Scheduler class for managing batch processing jobs"""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.scheduler = None
        self.setup_scheduler()
    
    def setup_scheduler(self):
        """Initialize and configure the scheduler"""
        try:
            # Configure job store to persist jobs in database
            jobstores = {
                'default': SQLAlchemyJobStore(url=self.database_url)
            }
            
            # Configure executors
            executors = {
                'default': Thread<PERSON>oolExecutor(20),
            }
            
            # Job defaults
            job_defaults = {
                'coalesce': False,
                'max_instances': 1,
                'misfire_grace_time': 300  # 5 minutes grace period
            }
            
            # Create scheduler
            self.scheduler = AsyncIOScheduler(
                jobstores=jobstores,
                executors=executors,
                job_defaults=job_defaults,
                timezone='Africa/Blantyre'  # Adjust to your timezone
            )
            
            # Add event listeners
            self.scheduler.add_listener(self.job_executed_listener, EVENT_JOB_EXECUTED)
            self.scheduler.add_listener(self.job_error_listener, EVENT_JOB_ERROR)
            
            logger.info("Scheduler configured successfully")
            
        except Exception as e:
            logger.error(f"Error setting up scheduler: {str(e)}")
            raise
    
    def start_scheduler(self):
        """Start the scheduler"""
        try:
            if not self.scheduler.running:
                self.scheduler.start()
                logger.info("Scheduler started successfully")
                
                # Schedule the daily batch job
                self.schedule_daily_batch()
            else:
                logger.info("Scheduler is already running")
                
        except Exception as e:
            logger.error(f"Error starting scheduler: {str(e)}")
            raise
    
    def stop_scheduler(self):
        """Stop the scheduler"""
        try:
            if self.scheduler.running:
                self.scheduler.shutdown()
                logger.info("Scheduler stopped successfully")
            else:
                logger.info("Scheduler is not running")
                
        except Exception as e:
            logger.error(f"Error stopping scheduler: {str(e)}")
            raise
    
    def schedule_daily_batch(self):
        """Schedule the daily batch processing job"""
        try:
            # Remove existing job if it exists
            try:
                self.scheduler.remove_job('daily_batch_processing')
            except:
                pass  # Job doesn't exist yet
            
            # Schedule daily batch processing at 1 AM
            self.scheduler.add_job(
                func=self.run_batch_processing,
                trigger='cron',
                hour=1,
                minute=0,
                id='daily_batch_processing',
                name='Daily Batch Processing',
                replace_existing=True
            )
            
            logger.info("Daily batch processing job scheduled for 1:00 AM")
            
        except Exception as e:
            logger.error(f"Error scheduling daily batch job: {str(e)}")
            raise
    
    def run_batch_processing(self):
        """Execute the batch processing"""
        try:
            logger.info("Starting scheduled batch processing")
            
            # Create and run batch processor
            processor = BatchProcessor()
            processor.run_daily_batch()
            
            logger.info("Scheduled batch processing completed successfully")
            
        except Exception as e:
            logger.error(f"Error in scheduled batch processing: {str(e)}")
            raise
    
    def run_batch_now(self):
        """Manually trigger batch processing (for testing)"""
        try:
            logger.info("Manually triggering batch processing")
            
            # Add a one-time job to run immediately
            self.scheduler.add_job(
                func=self.run_batch_processing,
                trigger='date',
                run_date=datetime.now(),
                id='manual_batch_processing',
                name='Manual Batch Processing',
                replace_existing=True
            )
            
            logger.info("Manual batch processing job added")
            
        except Exception as e:
            logger.error(f"Error triggering manual batch processing: {str(e)}")
            raise
    
    def get_scheduled_jobs(self):
        """Get list of scheduled jobs"""
        try:
            jobs = []
            for job in self.scheduler.get_jobs():
                jobs.append({
                    'id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger)
                })
            return jobs
            
        except Exception as e:
            logger.error(f"Error getting scheduled jobs: {str(e)}")
            return []
    
    def job_executed_listener(self, event):
        """Listener for successful job execution"""
        logger.info(f"Job {event.job_id} executed successfully at {event.scheduled_run_time}")
    
    def job_error_listener(self, event):
        """Listener for job execution errors"""
        logger.error(f"Job {event.job_id} failed: {event.exception}")
        logger.error(f"Traceback: {event.traceback}")

# Global scheduler instance
batch_scheduler = None

def get_scheduler(database_url: str = None) -> BatchScheduler:
    """Get or create the global scheduler instance"""
    global batch_scheduler
    
    if batch_scheduler is None:
        if database_url is None:
            # Use the same database URL as the main application
            from db import SQLALCHEMY_DATABASE_URL
            database_url = SQLALCHEMY_DATABASE_URL
            
        batch_scheduler = BatchScheduler(database_url)
    
    return batch_scheduler

def initialize_scheduler(database_url: str = None):
    """Initialize and start the scheduler"""
    try:
        scheduler = get_scheduler(database_url)
        scheduler.start_scheduler()
        logger.info("Batch scheduler initialized successfully")
        return scheduler
        
    except Exception as e:
        logger.error(f"Error initializing scheduler: {str(e)}")
        raise

def shutdown_scheduler():
    """Shutdown the scheduler"""
    global batch_scheduler
    
    try:
        if batch_scheduler:
            batch_scheduler.stop_scheduler()
            batch_scheduler = None
        logger.info("Batch scheduler shutdown successfully")
        
    except Exception as e:
        logger.error(f"Error shutting down scheduler: {str(e)}")
        raise

# Async wrapper for batch processing (if needed for FastAPI integration)
async def async_run_batch_processing():
    """Async wrapper for batch processing"""
    try:
        loop = asyncio.get_event_loop()
        processor = BatchProcessor()
        
        # Run in thread pool to avoid blocking the event loop
        await loop.run_in_executor(None, processor.run_daily_batch)
        
    except Exception as e:
        logger.error(f"Error in async batch processing: {str(e)}")
        raise
