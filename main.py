from fastapi import <PERSON>AP<PERSON>,Depends, HTTPException,status,BackgroundTasks as background_tasks
import models, schema
from db import get_db, Base
from sqlalchemy.orm import Session
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional, List, Annotated
from fastapi.security import <PERSON>A<PERSON>2Pass<PERSON><PERSON>earer, OAuth2PasswordRequestForm, HTTPBearer, HTTPAuthorizationCredentials
from passlib.context import CryptContext
# from auth import hash_password, verify_password, create_access_token, decode_access_token, get_current_user
from datetime import timedelta
# from routers import activities, beneficiaries, policy, payer,policyholder, policysummary, policymandate, imports, policy_management, reports, masterdata, beneficiaries_management, documents, premium_rates, mimo, dashboard_analytics, user_management, user_management_extended
import uvicorn
from scheduler import initialize_scheduler, shutdown_scheduler, get_scheduler
from batch_processor import BatchProcessor
import logging
from contextlib import asynccontextmanager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan events"""
    # Startup
    try:
        initialize_scheduler()
        logger.info("Application started with batch scheduler")
    except Exception as e:
        logger.error(f"Failed to initialize scheduler: {str(e)}")

    yield

    # Shutdown
    try:
        shutdown_scheduler()
        logger.info("Application shutdown with scheduler cleanup")
    except Exception as e:
        logger.error(f"Error during scheduler shutdown: {str(e)}")

app = FastAPI(lifespan=lifespan)
security = HTTPBearer()

db_dependency = Annotated[Session, Depends(get_db)]
# user_dependency = Annotated[dict, Depends(get_current_user)]

# Setting up CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

def prepare_premiumdue(policy: models.PolicyMandate):
    # from the firstdeduction date, calculate all the due dates up to date and store them in premiumdue table
    # print(policy.policyno)
    # print(policy.frequency)
    print(policy.firstdeductiondate)

# get all policies from policymandate limit 10
from fastapi import BackgroundTasks

@app.get("/policymandate10/", response_model=List[schema.PolicyMandate])
async def get_policymandate10(db: db_dependency, background_tasks: BackgroundTasks):
    policymandate = db.query(models.PolicyMandate).limit(10).all()
    for policy in policymandate:
        # call the function prepare_premiumdue and pass the policy as a parameter
        background_tasks.add_task(prepare_premiumdue, policy)
        # print(policy.policyno)
    return policymandate

# get all policies from policymandate limit 10
@app.get("/policymandate/", response_model=List[schema.PolicyMandate])
async def get_policymandate(db: db_dependency):
    # foreach loop through all policies and get the policyno,frequency,firstdeductiondate
    policymandate = db.query(models.PolicyMandate).all()
    for policy in policymandate:
        # call the function prepare_premiumdue and pass the policy as a parameter
        # background_tasks.add_task(prepare_premiumdue, policyno)
        print(policy.policyno)
    return policymandate

# Batch Processing API Endpoints
@app.post("/batch/run-now/")
async def run_batch_now(db: db_dependency):
    """Manually trigger batch processing (for testing)"""
    try:
        scheduler = get_scheduler()
        scheduler.run_batch_now()
        return {"message": "Batch processing triggered successfully", "status": "success"}
    except Exception as e:
        logger.error(f"Error triggering batch processing: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to trigger batch processing: {str(e)}")

@app.get("/batch/jobs/")
async def get_scheduled_jobs():
    """Get list of scheduled batch jobs"""
    try:
        scheduler = get_scheduler()
        jobs = scheduler.get_scheduled_jobs()
        return {"jobs": jobs, "status": "success"}
    except Exception as e:
        logger.error(f"Error getting scheduled jobs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get scheduled jobs: {str(e)}")

@app.post("/batch/test-individual-tasks/")
async def test_individual_tasks(db: db_dependency):
    """Test individual batch processing tasks (for development)"""
    try:
        processor = BatchProcessor()

        # Test each task individually
        results = {}

        try:
            processor.db = processor.get_db_session()

            # Test Task 1
            processor.generate_premium_due_activities()
            results["task1_premium_due"] = "success"

            # Test Task 2
            processor.create_premium_applied_activities()
            results["task2_premium_applied"] = "success"

            # Test Task 3
            processor.update_policy_summaries()
            results["task3_policy_summary"] = "success"

            # Test Task 4
            processor.create_policy_issued_activities()
            results["task4_policy_issued"] = "success"

            # Test Task 5
            processor.update_policy_status()
            results["task5_policy_status"] = "success"

            processor.db.commit()

        except Exception as e:
            if processor.db:
                processor.db.rollback()
            raise e
        finally:
            processor.close_db_session()

        return {"message": "Individual tasks tested successfully", "results": results, "status": "success"}

    except Exception as e:
        logger.error(f"Error testing individual tasks: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to test individual tasks: {str(e)}")

# # Department CRUD operations with Authentication
# @app.get("/checktoken/")
# def get_current_user(token: HTTPAuthorizationCredentials = Depends(security)):
#     user_data = decode_access_token(token.credentials)
#     if not user_data:
#         raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")
#     return user_data

# # signup user
# @app.post("/signup", response_model=schema.UserBase)
# def signup(user: schema.UserCreate, db: Session = Depends(get_db)):
#     hashed_password = hash_password(user.password)
#     user.password = hashed_password
#     user_model = models.User(**user.model_dump())
#     return services.create_user(db, user_model)

# # login user
# @app.post("/login")
# def login(user: schema.Userlogin, db: Session = Depends(get_db)):
#     db_user = db.query(models.User).filter(models.User.username == user.username).first()
#     if not db_user:
#         raise HTTPException(status_code=400, detail="1. Invalid username")

#     if not verify_password(user.password, db_user.password if isinstance(db_user.password, str) else db_user.password.value):
#         raise HTTPException(status_code=400, detail="2. Invalid password")

#     access_token = create_access_token(data={"sub": db_user.username, "id": db_user.id, "username": db_user.username}, expires_delta=timedelta(minutes=30))
#     return {"access_token": access_token, "token_type": "bearer", "user": db_user}

# # logout user
# @app.post("/logout")
# def logout(token: HTTPAuthorizationCredentials = Depends(security)):
#     # Verify the token is valid before logout
#     user_data = decode_access_token(token.credentials)
#     if not user_data:
#         raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication credentials")

#     # Since JWT tokens are stateless, we just return a success message
#     # The client should remove the token from storage
#     return {"message": "Successfully logged out", "status": "success"}