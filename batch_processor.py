"""
Batch Processing Module for Daily Policy Management Tasks
Runs daily at 1 AM to process premium dues, applications, and policy status updates
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
import models
from db import SessionLocal
from decimal import Decimal

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BatchProcessor:
    """Main batch processing class that orchestrates all daily tasks"""
    
    def __init__(self):
        self.db: Session = None
        self.batch_date = datetime.now().date()
        
    def get_db_session(self) -> Session:
        """Get database session"""
        if not self.db:
            self.db = SessionLocal()
        return self.db
    
    def close_db_session(self):
        """Close database session"""
        if self.db:
            self.db.close()
            self.db = None
    
    def run_daily_batch(self):
        """Main entry point for daily batch processing"""
        logger.info(f"Starting daily batch processing for {self.batch_date}")
        
        try:
            self.db = self.get_db_session()
            
            # Task 1: Generate Premium Due Activities
            logger.info("Starting Task 1: Premium Due Activity Generation")
            self.generate_premium_due_activities()
            
            # Task 2: Create Premium Applied Activities
            logger.info("Starting Task 2: Premium Applied Activity Creation")
            self.create_premium_applied_activities()
            
            # Task 3: Update Policy Summary
            logger.info("Starting Task 3: Policy Summary Updates")
            self.update_policy_summaries()
            
            # Task 4: Create Policy Issued Activities
            logger.info("Starting Task 4: Policy Issued Activity Creation")
            self.create_policy_issued_activities()
            
            # Task 5: Update Policy Status
            logger.info("Starting Task 5: Policy Status Updates")
            self.update_policy_status()
            
            self.db.commit()
            logger.info("Daily batch processing completed successfully")
            
        except Exception as e:
            logger.error(f"Error during batch processing: {str(e)}")
            if self.db:
                self.db.rollback()
            raise
        finally:
            self.close_db_session()
    
    def generate_premium_due_activities(self):
        """Task 1: Generate premium due activities for policies due today"""
        try:
            # Get all active policies with mandates
            policies = self.db.query(models.PolicyMandate).all()
            
            for policy in policies:
                if self.is_premium_due_today(policy):
                    self.create_premium_due_activity(policy)
                    
            logger.info(f"Completed premium due activity generation")
            
        except Exception as e:
            logger.error(f"Error in generate_premium_due_activities: {str(e)}")
            raise
    
    def is_premium_due_today(self, policy: models.PolicyMandate) -> bool:
        """Check if premium is due today based on frequency and first deduction date"""
        try:
            first_deduction = datetime.strptime(policy.firstdeductiondate, '%Y-%m-%d').date()
            
            # Calculate days since first deduction
            days_since_first = (self.batch_date - first_deduction).days
            
            if days_since_first < 0:
                return False  # Future date
            
            # Check based on frequency
            if policy.frequency.lower() == 'monthly':
                # Due on the same day each month
                return self.batch_date.day == first_deduction.day
            elif policy.frequency.lower() == 'quarterly':
                # Due every 3 months on the same day
                months_since = (self.batch_date.year - first_deduction.year) * 12 + (self.batch_date.month - first_deduction.month)
                return months_since % 3 == 0 and self.batch_date.day == first_deduction.day
            elif policy.frequency.lower() == 'annually':
                # Due annually on the same date
                return (self.batch_date.month == first_deduction.month and 
                       self.batch_date.day == first_deduction.day)
            elif policy.frequency.lower() == 'weekly':
                # Due every 7 days
                return days_since_first % 7 == 0
                
            return False
            
        except Exception as e:
            logger.error(f"Error checking if premium due for policy {policy.policyno}: {str(e)}")
            return False
    
    def create_premium_due_activity(self, policy: models.PolicyMandate):
        """Create premium due activity for a policy"""
        try:
            # Check if activity already exists for today
            existing_activity = self.db.query(models.PolicyActivities).filter(
                and_(
                    models.PolicyActivities.policyno == policy.policyno,
                    models.PolicyActivities.activitydesc == "Premium Due",
                    models.PolicyActivities.effectivedate == str(self.batch_date)
                )
            ).first()
            
            if existing_activity:
                logger.info(f"Premium due activity already exists for policy {policy.policyno}")
                return
            
            # Create new premium due activity
            activity = models.PolicyActivities(
                policyno=policy.policyno,
                activitydesc="Premium Due",
                remarks=policy.premium,
                effectivedate=str(self.batch_date),
                activitydetails="Premium Due",
                datecreated=str(datetime.now()),
                createdby=0  # System generated
            )
            
            self.db.add(activity)
            logger.info(f"Created premium due activity for policy {policy.policyno}")
            
        except Exception as e:
            logger.error(f"Error creating premium due activity for policy {policy.policyno}: {str(e)}")
            raise
    
    def create_premium_applied_activities(self):
        """Task 2: Create premium applied activities based on suspense amounts"""
        try:
            # Get policies with premium due this month
            current_month_start = self.batch_date.replace(day=1)
            current_month_end = (current_month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
            
            policies_with_due = self.db.query(models.PolicyActivities.policyno).filter(
                and_(
                    models.PolicyActivities.activitydesc == "Premium Due",
                    models.PolicyActivities.effectivedate >= str(current_month_start),
                    models.PolicyActivities.effectivedate <= str(current_month_end)
                )
            ).distinct().all()
            
            for policy_tuple in policies_with_due:
                policy_no = policy_tuple[0]
                self.process_premium_application(policy_no)
                
            logger.info("Completed premium applied activity creation")
            
        except Exception as e:
            logger.error(f"Error in create_premium_applied_activities: {str(e)}")
            raise
    
    def process_premium_application(self, policy_no: str):
        """Process premium application for a specific policy"""
        try:
            # Get the most recent suspense amount activity
            suspense_activity = self.db.query(models.PolicyActivities).filter(
                and_(
                    models.PolicyActivities.policyno == policy_no,
                    models.PolicyActivities.activitydesc == "Suspense Amount"
                )
            ).order_by(models.PolicyActivities.id.desc()).first()
            
            if not suspense_activity:
                logger.info(f"No suspense amount found for policy {policy_no}")
                return
            
            # Get premium amount from policy mandate
            policy_mandate = self.db.query(models.PolicyMandate).filter(
                models.PolicyMandate.policyno == policy_no
            ).first()
            
            if not policy_mandate:
                logger.warning(f"No policy mandate found for policy {policy_no}")
                return
            
            suspense_amount = Decimal(suspense_activity.remarks)
            premium_amount = Decimal(policy_mandate.premium)
            
            if suspense_amount >= premium_amount:
                # Create premium applied activity
                self.create_premium_applied_activity(policy_no, premium_amount)
                
                # Update suspense amount
                new_suspense = suspense_amount - premium_amount
                self.create_updated_suspense_activity(policy_no, new_suspense)
                
        except Exception as e:
            logger.error(f"Error processing premium application for policy {policy_no}: {str(e)}")
            raise
    
    def create_premium_applied_activity(self, policy_no: str, premium_amount: Decimal):
        """Create premium applied activity"""
        activity = models.PolicyActivities(
            policyno=policy_no,
            activitydesc="Premium Applied",
            remarks=str(premium_amount),
            effectivedate=str(self.batch_date),
            activitydetails="Premium Applied",
            datecreated=str(datetime.now()),
            createdby=0
        )
        self.db.add(activity)
        logger.info(f"Created premium applied activity for policy {policy_no}")
    
    def create_updated_suspense_activity(self, policy_no: str, new_suspense: Decimal):
        """Create updated suspense amount activity"""
        activity = models.PolicyActivities(
            policyno=policy_no,
            activitydesc="Suspense Amount",
            remarks=str(new_suspense),
            effectivedate=str(self.batch_date),
            activitydetails="Suspense Amount",
            datecreated=str(datetime.now()),
            createdby=0
        )
        self.db.add(activity)
        logger.info(f"Updated suspense amount for policy {policy_no}: {new_suspense}")

    def update_policy_summaries(self):
        """Task 3: Update policy summary table for all policies"""
        try:
            # Get all unique policy numbers
            policy_numbers = self.db.query(models.PolicyActivities.policyno).distinct().all()

            for policy_tuple in policy_numbers:
                policy_no = policy_tuple[0]
                self.update_policy_summary(policy_no)

            logger.info("Completed policy summary updates")

        except Exception as e:
            logger.error(f"Error in update_policy_summaries: {str(e)}")
            raise

    def update_policy_summary(self, policy_no: str):
        """Update or create policy summary for a specific policy"""
        try:
            # Count premium due activities
            premiums_due = self.db.query(func.count(models.PolicyActivities.id)).filter(
                and_(
                    models.PolicyActivities.policyno == policy_no,
                    models.PolicyActivities.activitydesc == "Premium Due"
                )
            ).scalar()

            # Count premium applied activities
            premiums_paid = self.db.query(func.count(models.PolicyActivities.id)).filter(
                and_(
                    models.PolicyActivities.policyno == policy_no,
                    models.PolicyActivities.activitydesc == "Premium Applied"
                )
            ).scalar()

            # Sum of Money In activities
            total_money_in = self.db.query(func.sum(
                func.cast(models.PolicyActivities.remarks, models.PolicyActivities.remarks.type)
            )).filter(
                and_(
                    models.PolicyActivities.policyno == policy_no,
                    models.PolicyActivities.activitydesc == "Money In"
                )
            ).scalar() or 0

            # Calculate outstanding
            outstanding = premiums_due - premiums_paid

            # Check if policy summary exists
            existing_summary = self.db.query(models.PolicySummary).filter(
                models.PolicySummary.policyno == policy_no
            ).first()

            if existing_summary:
                # Update existing record
                existing_summary.premiumsdue = str(premiums_due)
                existing_summary.premiumspaid = str(premiums_paid)
                existing_summary.totalpremiumspaid = str(total_money_in)
                existing_summary.outstanding = str(outstanding)
            else:
                # Create new record
                new_summary = models.PolicySummary(
                    policyno=policy_no,
                    premiumsdue=str(premiums_due),
                    premiumspaid=str(premiums_paid),
                    totalpremiumspaid=str(total_money_in),
                    outstanding=str(outstanding)
                )
                self.db.add(new_summary)

            logger.info(f"Updated policy summary for {policy_no}: Due={premiums_due}, Paid={premiums_paid}, Outstanding={outstanding}")

        except Exception as e:
            logger.error(f"Error updating policy summary for {policy_no}: {str(e)}")
            raise

    def create_policy_issued_activities(self):
        """Task 4: Create policy issued activities based on first premium applied"""
        try:
            # Get policies with premium applied activities but no policy issued activity
            policies_with_applied = self.db.query(models.PolicyActivities.policyno).filter(
                models.PolicyActivities.activitydesc == "Premium Applied"
            ).distinct().all()

            for policy_tuple in policies_with_applied:
                policy_no = policy_tuple[0]

                # Check if policy issued activity already exists
                existing_issued = self.db.query(models.PolicyActivities).filter(
                    and_(
                        models.PolicyActivities.policyno == policy_no,
                        models.PolicyActivities.activitydesc == "Policy Issued"
                    )
                ).first()

                if not existing_issued:
                    self.create_policy_issued_activity(policy_no)

            logger.info("Completed policy issued activity creation")

        except Exception as e:
            logger.error(f"Error in create_policy_issued_activities: {str(e)}")
            raise

    def create_policy_issued_activity(self, policy_no: str):
        """Create policy issued activity for a specific policy"""
        try:
            # Get the first premium applied activity
            first_premium_applied = self.db.query(models.PolicyActivities).filter(
                and_(
                    models.PolicyActivities.policyno == policy_no,
                    models.PolicyActivities.activitydesc == "Premium Applied"
                )
            ).order_by(models.PolicyActivities.id.asc()).first()

            if not first_premium_applied:
                return

            # Create policy issued activity
            activity = models.PolicyActivities(
                policyno=policy_no,
                activitydesc="Policy Issued",
                remarks=first_premium_applied.effectivedate,
                effectivedate=first_premium_applied.effectivedate,
                activitydetails="Policy Issued",
                datecreated=str(datetime.now()),
                createdby=0
            )

            self.db.add(activity)
            logger.info(f"Created policy issued activity for policy {policy_no}")

        except Exception as e:
            logger.error(f"Error creating policy issued activity for policy {policy_no}: {str(e)}")
            raise

    def update_policy_status(self):
        """Task 5: Update policy status based on outstanding premiums"""
        try:
            # Get all policy summaries
            policy_summaries = self.db.query(models.PolicySummary).all()

            for summary in policy_summaries:
                self.update_individual_policy_status(summary)

            logger.info("Completed policy status updates")

        except Exception as e:
            logger.error(f"Error in update_policy_status: {str(e)}")
            raise

    def update_individual_policy_status(self, summary: models.PolicySummary):
        """Update status for an individual policy based on outstanding premiums"""
        try:
            outstanding = int(summary.outstanding)

            # Determine status based on outstanding premiums
            if outstanding == 1:
                new_status = "Grace"
            elif outstanding == 0:
                new_status = "Active"
            elif 1 < outstanding < 5:
                new_status = "Reinstatement"
            elif outstanding >= 5:
                new_status = "Lapse"
            else:
                new_status = "Unknown"

            # Update policy status
            policy = self.db.query(models.Policy).filter(
                models.Policy.policyno == summary.policyno
            ).first()

            if policy:
                policy.status = new_status
                logger.info(f"Updated policy {summary.policyno} status to {new_status} (Outstanding: {outstanding})")
            else:
                logger.warning(f"Policy {summary.policyno} not found in policy table")

        except Exception as e:
            logger.error(f"Error updating status for policy {summary.policyno}: {str(e)}")
            raise
